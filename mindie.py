import re
import uuid

class MindIEToolCallStreamParser:

    def __init__(self):
        self.tool_call_id = -1
        self.tool_call_buffer = ""
        self.tool_call_name = ""
        self.tool_call_arguments = False
        self.tool_call_name_pattern = r'"name":\s*"([^"]*)"'
        self.tool_call_arguments_pattern = r'"arguments":\s*()'
        self.in_tool_call = False
        self.tool_call_start = '<tool_call>'
        self.tool_call_end = '</tool_call>'
        self.left_parentheses_count = -1     # 记录参数的左括号的数量

    
    def get_tool_call_id(self):
        return f"call_{uuid.uuid4().hex[:12]}_{self.tool_call_id}"
    
    def parse_stream_tool_call(self, delta_text: str):
        if self.tool_call_start in delta_text:
            index = delta_text.find(self.tool_call_start)
            # <tool_call> 之前的内容以正常流返回
            if index > 0: 
                yield {"content": delta_text[:index]}
            # 开始解析 <tool_call> 内容
            self.tool_call_id += 1
            self.in_tool_call = True
            self.left_parentheses_count = -1
            delta_text = delta_text[index+len(self.tool_call_start):]
            if delta_text:
                return self.parse_stream_tool_call(delta_text)

        elif self.tool_call_end in delta_text:
            index = delta_text.find(self.tool_call_end)
            # </tool_call> 之前的内容按照工具调用格式返回
            temp_str = delta_text[:index]
            return_delta_str = ""
            for char in temp_str:
                if char == "{": self.left_parentheses_count += 2 if self.left_parentheses_count == -1 else 1
                elif char == "}": self.left_parentheses_count -= 1
                return_delta_str += char
                if self.left_parentheses_count == 0:
                    break
            if return_delta_str:
                yield {
                    "tool_calls":[
                        {
                            "index": self.tool_call_id,
                            "function": {"arguments": return_delta_str}
                        }
                    ]
                }
            
            # 解析 <tool_call> 内容结束，剩余的内容以正常流返回
            self.in_tool_call = False
            self.tool_call_name = ""
            self.tool_call_buffer = ""
            self.tool_call_arguments = False
            self.left_parentheses_count = -1
            delta_text = delta_text[index+len(self.tool_call_end):]     
            if delta_text:
                return self.parse_stream_tool_call(delta_text)

        elif self.in_tool_call:
            if self.tool_call_name == "":
                self.tool_call_buffer += delta_text
                match = re.search(self.tool_call_name_pattern, self.tool_call_buffer)
                # 解析出工具调用名称，之后的都是解析参数
                if match:
                    self.tool_call_name = match.group(1)
                    self.tool_call_buffer = self.tool_call_buffer[match.end():]
                    yield {
                        "tool_calls":[
                            {
                                "id": self.get_tool_call_id(),
                                "type": "function",
                                "index": self.tool_call_id,
                                "function": {"name": self.tool_call_name}
                            }
                        ]
                    }
            else:
                if self.tool_call_arguments:
                    return_delta_str = ""
                    return_delta_str_start_index = 0
                    for i, char in enumerate(delta_text):
                        if char == "{": self.left_parentheses_count += 2 if self.left_parentheses_count == -1 else 1
                        elif char == "}": self.left_parentheses_count -= 1
                        if self.left_parentheses_count == -1:
                            # 跳过空格，只有当遇到{才开始返回
                            continue
                        return_delta_str_start_index = i if return_delta_str_start_index == 0 else return_delta_str_start_index
                        return_delta_str += char
                        if self.left_parentheses_count == 0:
                            break
                    if return_delta_str:
                        yield {
                            "tool_calls":[
                                {
                                    "index": self.tool_call_id,
                                    "function": {"arguments": return_delta_str}
                                }
                            ]
                        }
                    # 如果return_delta_str的长度小于delta_text，说明参数解析完后还有内容，以content返回
                    # if len(return_delta_str) < len(delta_text[return_delta_str_start_index:]):
                    #     yield {"content": delta_text[len(return_delta_str):]}
                    # 参数解析完后，剩余内容不返回

                else:
                    self.tool_call_buffer += delta_text
                    match = re.search(self.tool_call_arguments_pattern, self.tool_call_buffer)
                    # 解析出工具调用的参数
                    if match:
                        self.tool_call_arguments = True
                        tool_call_arguments_buffer = self.tool_call_buffer[match.end():]
                        self.tool_call_buffer = ""
                        return_delta_str = ""
                        return_delta_str_start_index = 0
                        for i, char in enumerate(tool_call_arguments_buffer):
                            if char == "{": self.left_parentheses_count += 2 if self.left_parentheses_count == -1 else 1
                            elif char == "}": self.left_parentheses_count -= 1
                            if self.left_parentheses_count == -1:
                                # 跳过空格，只有当遇到{才开始返回
                                continue
                            return_delta_str_start_index = i if return_delta_str_start_index == 0 else return_delta_str_start_index
                            return_delta_str += char
                            if self.left_parentheses_count == 0:
                                break
                        if return_delta_str:
                            yield {
                                "tool_calls":[
                                    {
                                        "index": self.tool_call_id,
                                        "function": {"arguments": return_delta_str}
                                    }
                                ]
                            }
                        # return_delta_str的长度小于tool_call_arguments_buffer，说明参数解析完后还有内容，以content返回
                        # if len(return_delta_str) < len(tool_call_arguments_buffer[return_delta_str_start_index:]):
                        #     yield {"content": tool_call_arguments_buffer[return_delta_str_start_index+len(return_delta_str):]}
                        # 参数解析完后，剩余内容不返回
        else:
            yield {"content": delta_text}
