import os

from flask import Flask, request, Response
from flask_cors import CORS
import requests, json, re, uuid
from mindie import MindIEToolCallStreamParser

app = Flask(__name__)
CORS(app)

# MINDIE_LLM_URL = os.environ.get('MINDIE_LLM_URL')
MINDIE_LLM_URL = "https://ai.secsign.online:3003"


@app.route("/v1/models", methods=["GET"])
def list_models():
    try:
        # 转发模型列表请求
        mindie_service_url = f"{MINDIE_LLM_URL}/v1/models"
        headers = {
            key: value
            for key, value in request.headers.items()
            if key not in ["Content-Length", "Host", "Transfer-Encoding"]
        }
        response = requests.get(mindie_service_url, headers=headers, timeout=10)
        
        if response.status_code >= 400:
            return {"error": f"MindIE 服务错误: {response.status_code}"}, response.status_code
        
        result = response.json()
        return result
        
    except requests.exceptions.ConnectionError:
        return {"error": "无法连接到 MindIE 服务"}, 503
    except requests.exceptions.Timeout:
        return {"error": "MindIE 服务响应超时"}, 504
    except Exception as e:
        return {"error": f"获取模型列表失败: {str(e)}"}, 500

def stream_response(resp):
    parser = MindIEToolCallStreamParser()
    MAX_CHUNK_SIZE = 1024 * 10  # 10KB 最大chunk大小

    def generate_stream():
        stop_reason = ""
        buffer = ""  # 添加缓冲区防止数据截断
        
        # 累积所有内容，包括工具调用
        accumulated_data = []
        
        for chunk in resp.iter_content(chunk_size=1024):
            if chunk:
                chunk_str = chunk.decode("utf-8")
                buffer += chunk_str
                
                # 按行分割，但保持完整性
                lines = buffer.split("\n")
                buffer = lines[-1]  # 保留最后一行（可能不完整）
                
                for line in lines[:-1]:  # 处理完整的行
                    if line.startswith("data: "):
                        # 提取JSON部分
                        json_str = line[6:]  # 去掉 'data: ' 前缀
                        if json_str.strip() == "[DONE]":
                            accumulated_data.append(f"data: {json_str}\n\n")
                            continue
                        try:
                            json_data = json.loads(json_str)
                            # 提取content

                            if (
                                json_data.get("choices")
                                and len(json_data["choices"]) > 0
                                and json_data["choices"][0].get("delta")
                                and "tool_calls" in json_data["choices"][0]["delta"]
                            ):
                                # 安全序列化tool_calls数据，防止截断
                                try:
                                    serialized_data = json.dumps(json_data, ensure_ascii=False, separators=(',', ':'))
                                    # 检查数据大小，防止过大的数据
                                    if len(serialized_data) > MAX_CHUNK_SIZE:
                                        print(f"Tool calls数据过大，跳过: {len(serialized_data)} bytes")
                                        continue
                                    accumulated_data.append(f"data: {serialized_data}\n\n")
                                except (TypeError, ValueError) as e:
                                    # 如果序列化失败，记录错误并跳过
                                    print(f"Tool calls序列化失败: {e}")
                                    continue
                                continue
                            
                            if (
                                json_data.get("choices")
                                and len(json_data["choices"]) > 0
                                and json_data["choices"][0].get("delta")
                                and "content" in json_data["choices"][0]["delta"]
                            ):
                                content = json_data["choices"][0]["delta"].pop("content")
                                for parsed_chunk in parser.parse_stream_tool_call(content):
                                    if ("choices" in json_data and len(json_data["choices"]) > 0):
                                        json_data["choices"][0]["delta"].update(parsed_chunk)

                            # 返回处理后的数据
                            if len(json_data["choices"][0]["delta"]) > 1:
                                if "tool_calls" in json_data["choices"][0]["delta"]:
                                    stop_reason = "tool_calls"
                                    if "role" in json_data["choices"][0]["delta"]:
                                        del json_data["choices"][0]["delta"]["role"]
                                    if "content" in json_data["choices"][0]["delta"]:
                                        del json_data["choices"][0]["delta"]["content"]
                                try:
                                     if (
                                         stop_reason == "tool_calls"
                                         and json_data["choices"][0]["finish_reason"]
                                         == "stop"
                                     ):
                                         json_data["choices"][0][
                                             "finish_reason"
                                         ] = "tool_calls"
                                         del json_data["choices"][0]["delta"]["role"]
                                         json_data["choices"][0]["stop_reason"] = None
                                except:
                                     pass
                                # 安全序列化，防止数据截断
                                try:
                                    serialized_data = json.dumps(json_data, ensure_ascii=False, separators=(',', ':'))
                                    # 检查数据大小，防止过大的数据
                                    if len(serialized_data) > MAX_CHUNK_SIZE:
                                        print(f"数据过大，跳过: {len(serialized_data)} bytes")
                                        continue
                                    accumulated_data.append(f"data: {serialized_data}\n\n")
                                except (TypeError, ValueError) as e:
                                    print(f"数据序列化失败: {e}")
                                    continue

                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}, 数据: {json_str}")
                            accumulated_data.append(line + "\n")
                        except Exception as e:
                            print(f"处理错误: {e}")
                            accumulated_data.append(line + "\n")
                    else:
                        if line:
                            accumulated_data.append(line + "\n\n")
        
        # 处理缓冲区中剩余的数据
        if buffer.strip():
            if buffer.startswith("data: "):
                json_str = buffer[6:]
                if json_str.strip() == "[DONE]":
                    accumulated_data.append(f"data: {json_str}\n\n")
                else:
                    try:
                        json_data = json.loads(json_str)
                        serialized_data = json.dumps(json_data, ensure_ascii=False, separators=(',', ':'))
                        accumulated_data.append(f"data: {serialized_data}\n\n")
                    except:
                        accumulated_data.append(f"data: {json_str}\n\n")
            else:
                accumulated_data.append(buffer + "\n\n")
                
        # 一次性将所有累积的数据以流式方式发送
        for data in accumulated_data:
            yield data

    return Response(
        generate_stream(),
        status=resp.status_code,
        headers={
            k: v
            for k, v in resp.headers.items()
            if k.lower() not in ["transfer-encoding"]
        },
        content_type=resp.headers.get("content-type"),
    )


@app.route("/v1/completions", methods=["POST"])
def completions():
    try:
        COMPLETIONS_URL = f"{MINDIE_LLM_URL}/v1/completions"
        stream = request.json.get("stream", False)
        if stream:
            response = requests.post(
                COMPLETIONS_URL, json=request.json, headers=request.headers, stream=True
            )
            return stream_response(response)
        else:
            response = requests.post(
                COMPLETIONS_URL, json=request.json, headers=request.headers
            )
            return response.json()
    except Exception as e:
        return {"error": f"处理请求失败: {str(e)}"}, 500


def replace_empty_content(request_body):
    """
    替换content为空的情况。
    """
    for message in request_body.get("messages", []):
        content = message.get("content", "")
        if isinstance(content, str) and content == "":
            message["content"] = " "
        elif isinstance(content, dict) and content.get("text", "") == "":
            content["text"] = " "
    return request_body


def handle_tool_call(response_body):
    """
    提取响应消息中的工具调用内容并生成标准tool call参数
    """
    # tool_call_pattern = re.compile(r'\{"name":\s*"([^"]+)",\s*"arguments":\s*({[^}]+})}', re.DOTALL)
    tool_call_pattern = re.compile(
        r'\{"name"\s*:\s*"([^"]+)"\s*,\s*"arguments"\s*:\s*(?:"({.*?})"|({.*?}))\s*\}',
        re.DOTALL,
    )

    choices = response_body.get("choices", [])
    for choice in choices:
        message = choice.get("message", {})
        content = message.get("content", "")

        if isinstance(content, str):
            tool_calls = []
            clean_content = content

            # 使用正则表达式匹配工具调用参数

        matches = tool_call_pattern.finditer(content)
        for match in matches:
            try:
                # 判断匹配类型
                if match.group(2):  # 字符串包裹的 JSON
                    expr_str = match.group(2)
                    unescaped_str = json.loads(f'"{expr_str}"')
                    arguments = json.loads(unescaped_str)
                else:  # 直接 JSON 对象
                    expr_str = match.group(3)
                    arguments = json.loads(expr_str)

                tool_calls.append(
                    {
                        "id": f"call_{uuid.uuid4().hex[:12]}",
                        "type": "function",
                        "function": {
                            "name": match.group(1),
                            "arguments": json.dumps(arguments, ensure_ascii=False),
                        },
                    }
                )

            except json.JSONDecodeError as e:
                print(f"解析失败: {e}")
                continue

            if tool_calls:
                message["tool_calls"] = tool_calls
                message["content"] = clean_content
                choice["finish_reason"] = "tool_calls"

    return response_body


def handle_stream_tool_call(response_body):
    pass

@app.route("/v1/chat/completions", methods=["POST"])
def chat_completions():
    try:
        CHAT_COMPLETIONS_URL = f"{MINDIE_LLM_URL}/v1/chat/completions"
        request_body = request.json
        stream = request_body.get("stream", False)
        
        messages = request_body.get("messages", [])
        for i in range(len(messages)):
            if isinstance(messages[i]["content"], list):
                final_content = ""
                for content in messages[i]["content"]:
                    if content["type"] == "text":
                        final_content += '\n' + content["text"]
                    else:
                        return {"error": f"不支持的类型: {content['type']}"}, 500
                messages[i]["content"] = final_content
            if messages[i]["role"] == "assistant" and "tool_calls" in messages[i]:
                for j in range(len(messages[i]["tool_calls"])):
                    arguments = messages[i]["tool_calls"][j]["function"]["arguments"]
                    if isinstance(arguments, dict):
                        arguments = json.dumps(arguments)
                        messages[i]["tool_calls"][j]["function"]["arguments"] = arguments
        request_body["messages"] = messages

        tools = request_body.get("tools", [])
        for i in range(len(tools)):
            if "type" not in tools[i]:
                tools[i] = {"type": "function", "function": tools[i]}
        request_body["tools"] = tools

        request_body = replace_empty_content(request_body)
        if stream:
            response = requests.post(
                CHAT_COMPLETIONS_URL, json=request_body, headers=request.headers, stream=True
            )
            return stream_response(response)
        else:
            response = requests.post(
                CHAT_COMPLETIONS_URL, json=request_body, headers=request.headers
            )
            response_json = response.json()
            response_text = response_json["choices"][0]["message"]["content"]
            # 替换掉qwen3-coder-480b-int8中的```
            response_json["choices"][0]["message"]["content"] = response_text.replace("``", "")
            response_json = handle_tool_call(response_json)
            return response_json
    except Exception as e:
        # request_bod写入eror.json
        with open("error.json", "w") as f:
            json.dump(request.json, f)
        with open("error-info.txt", "a") as f:
            f.write(f"处理请求失败: {str(e)}\n")
        import traceback
        traceback.print_exc()
        return {"error": f"处理请求失败: {str(e)}"}, 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000)