# 请求示例
{
  "model": "qwen_coder",
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的密码学算法助手，精通各类国际密码算法和GM标准算法。\\n#密码算法步骤：1.用户需要提供密钥ID 2.如果用户未提供密钥ID，则可调用query_keys查询密钥列表，并使用第1个符合条件的密钥ID。如果密钥列表为空，则需要调用generate_key生成密钥。"
    },
    {
      "role": "user",
      "content": "使用sm4算法加密数据：明文"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "generate_key",
        "description": "\n    \u751f\u6210\u65b0\u7684\u52a0\u5bc6\u5bc6\u94a5\n    \u53c2\u6570:\n        key_type: \u5bc6\u94a5\u7c7b\u578b (SM2, SM4, AES, RSA)\n    \u8fd4\u56de:\n        key_id: \u751f\u6210\u7684\u5bc6\u94a5ID (\u6574\u6570\u7c7b\u578b)\n    ",
        "parameters": {
          "properties": {
            "key_type": {
              "title": "Key Type",
              "type": "string"
            }
          },
          "required": [
            "key_type"
          ],
          "title": "generate_keyArguments",
          "type": "object"
        }
      }
    }
  ],
  "tool_choice": "auto"
}
# 无工具支持的响应 需要转换
转换之前的响应：
{
    "choices": [
        {
            "finish_reason": "stop",
            "index": 0,
            "message": {
                "content": "首先，我们需要确定是否已经有一个可用的SM4密钥ID。如果没有提供密钥ID，我们将查询现有的SM4密钥列表。如果列表为空，我们将生成一个新的SM4密钥。\n\n让我们从查询现有的SM4密钥开始：\n\n{\"name\": \"query_keys\", \"arguments\": {\"key_type\": \"SM4\"}}",
                "role": "assistant",
                "tool_calls": null
            }
        }
    ],
    "created": 1744683735
}
转换之后：
{
    "choices": [
        {
            "finish_reason": "tool_calls",
            "index": 0,
            "message": {
                "content": "首先，我们需要确定是否已经有一个可用的SM4密钥ID。如果没有提供密钥ID，我们将查询现有的SM4密钥列表。如果列表为空，我们将生成一个新的SM4密钥。\n\n让我们从查询现有的SM4密钥开始：",
                "role": "assistant",
                "tool_calls": [
                    {
                        "function": {
                            "arguments": "{\"key_type\": \"SM4\"}",
                            "name": "query_keys"
                        },
                        "id": "call_BRUWYXgm",
                        "type": "function"
                    }
                ]
            }
        }
    ],
    "created": 1744684088
}