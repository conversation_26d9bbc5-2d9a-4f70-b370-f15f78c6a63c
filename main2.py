import configparser
from flask import Flask, request, jsonify
import requests, json, re, uuid
import logging

CHAT_MODEL = "chat"
MULTI_MODEL = "multi"
def load_model_config(config_file_path):
    """
    加载模型配置文件并返回模型类型映射。
    """
    config = configparser.ConfigParser()
    config.read(config_file_path)
    model_mapping = {}
    mindie_config = {}
    
    # 加载模型类型映射
    if 'models' in config:
        for model, model_type in config['models'].items():
            types = model_type.lower().split(',')
            model_mapping[model] = {
                "type": types[0],
                "tool": "tool" in types
            }
    
    # 加载MindIE服务地址配置
    if 'mindie' in config:
        mindie_config = {
            "default": config['mindie'].get("default", "http://10.0.51.251:3000"),
            "embed": config['mindie'].get("embed", "http://10.0.51.251:18080")
        }
    
    return model_mapping, mindie_config

def replace_empty_content(request_body):
    """
    替换content为空的情况。
    """
    for message in request_body.get("messages", []):
        content = message.get("content")
        if isinstance(content, str) and content == "":
            message["content"] = " "
        elif isinstance(content, dict) and content.get("text") == "":
            content["text"] = " "
    return request_body

def convert_dialogue_model(request_body):
    """
    转换对话模型的API body。
    """
    for message in request_body.get("messages", []):
        content = message.get("content")
        
        # 处理 content 为字典的情况
        if isinstance(content, dict) and content.get("type") == "text":
            message["content"] = content.get("text", "")
        
        # 处理 content 为列表的情况
        elif isinstance(content, list):
            # 提取所有 text 字段并拼接成字符串
            text_content = " ".join(
                item.get("text", "") for item in content if isinstance(item, dict) and item.get("type") == "text"
            )
            message["content"] = text_content
    
    return request_body

def convert_multimodal_model(request_body):
    """
    转换多模态模型的API body。
    """
    for message in request_body.get("messages", []):
        content = message.get("content")
        if isinstance(content, str):
            message["content"] = {"type": "text", "text": content}
    return request_body

def identify_model_type(request_body, model_mapping):
    """
    识别模型类型。
    """
    model_name = request_body.get("model")
    # 根据配置文件判断模型类型
    model_type = model_mapping.get(model_name, CHAT_MODEL)
    return model_type

def map_response(response):
    """
    将华为mindIE框架的返回结果映射回openai兼容的格式。
    """
    # 示例映射逻辑
    mapped_response = {
        "choices": [
            {
                "message": {
                    "role": "assistant",
                    "content": response.get("result", "")
                }
            }
        ]
    }
    return mapped_response

app = Flask(__name__)
@app.before_request
def log_request_info():
    app.logger.info('Request: %s %s', request.method, request.url)

def convert_body(request, backend_url, method="POST"):
    """
    转发请求到后台服务，并支持流式响应。
    """
    # 特殊处理嵌入模型的独立后端地址
    if backend_url == "/embed":
        mindie_service_url = mindie_addreess["embed"] + backend_url
    else:
        mindie_service_url = mindie_addreess["default"] + backend_url
    
    
    # 检查 Transfer-Encoding 是否为 chunked
    transfer_encoding = request.headers.get('Transfer-Encoding', '').lower()
    if transfer_encoding == 'chunked':
        # 解析 chunked 数据
        request_body = parse_chunked_data(request.stream)
    else:
        request_body = request.json

    headers = {key: value for key, value in request.headers.items() if key not in ['Content-Length', 'Host', 'Transfer-Encoding']}

    if method == "POST":
        request_body = replace_empty_content(request_body)
        
        model_name = request_body.get("model")
        model_info = model_mapping.get(model_name, {"type": CHAT_MODEL, "tool": False})
        
        # 根据模型类型转换API body
        # 处理嵌入模型的特殊转换
        if model_info["type"] == "embedding":
            # 转换请求体格式
            request_body = convert_embedding_model(request_body)
        elif model_info["type"] == CHAT_MODEL:
            request_body = convert_dialogue_model(request_body)
        elif model_info["type"] == MULTI_MODEL:
            request_body = convert_multimodal_model(request_body)

        stream = request_body.get("stream", False)
        if stream:
            response = requests.post(
                mindie_service_url,
                json=request_body,
                headers=headers,
                stream=True
            )
            return stream_response(response)
        else:
            response = requests.post(mindie_service_url, json=request_body, headers=headers)
            if response.status_code >= 400:
                print(f"API Error, URL: {mindie_service_url}, Request body: {request_body}, Response: {response.content}")
                # 错误信息记录到日志中
                logging.error(f"API Error, URL: {mindie_service_url}, Request body: {request_body}, Response: {response.content}")
                return response.content, response.status_code, response.headers.items()            
            try:
                if model_info["type"] == "embedding":
                    # 处理嵌入模型的特殊转换
                    response_json = map_embedding_response(response.json(), model_name)
                    return response_json
                else:
                    response_json = response.json()
                    if response_json.get("error"):
                        print(f"Error: {response_json['error']}")
                        return response_json
                    # 判断是否需要处理工具调用
                    tool_choice = request_body.get("tool_choice")
                    if tool_choice == "auto" and not model_info["tool"]:
                        # 对响应消息进行工具提取转换
                        response_json = handle_tool_call(response_json)
                        return response_json                
            except Exception as e:
                print(f"Error: {e}， Response content: {response.content}")
                return {"error": str(e)}
    else:
        response = requests.get(mindie_service_url, headers=headers)

    if response.status_code >= 400:
        return response.content, response.status_code, response.headers.items()

    return response.json()

def handle_tool_call(response_body):
    """
    提取响应消息中的工具调用内容并生成标准tool call参数
    """
    #tool_call_pattern = re.compile(r'\{"name":\s*"([^"]+)",\s*"arguments":\s*({[^}]+})}', re.DOTALL)
    tool_call_pattern = re.compile(
        r'\{"name"\s*:\s*"([^"]+)"\s*,\s*"arguments"\s*:\s*(?:"({.*?})"|({.*?}))\s*\}',
        re.DOTALL
    )
    
    choices = response_body.get("choices", [])
    for choice in choices:
        message = choice.get("message", {})
        content = message.get("content", "")
        
        if isinstance(content, str):
            tool_calls = []
            clean_content = content
            
            # 使用正则表达式匹配工具调用参数
            
        matches = tool_call_pattern.finditer(content)
        for match in matches:
            try:
                # 判断匹配类型
                if match.group(2):  # 字符串包裹的 JSON
                    expr_str = match.group(2)
                    unescaped_str = json.loads(f'"{expr_str}"')
                    arguments = json.loads(unescaped_str)
                else:  # 直接 JSON 对象
                    expr_str = match.group(3)
                    arguments = json.loads(expr_str)
                
                tool_calls.append({
                    "id": f"call_{uuid.uuid4().hex[:12]}",
                    "type": "function",
                    "function": {
                        "name": match.group(1),
                        "arguments": json.dumps(arguments, ensure_ascii=False)
                    }
                })
                
            except json.JSONDecodeError as e:
                print(f"解析失败: {e}")
                continue
            
            if tool_calls:
                message["tool_calls"] = tool_calls
                message["content"] = clean_content
                choice["finish_reason"] = "tool_calls"
    
    return response_body

def convert_embedding_model(request_body):
    """
    将OpenAI嵌入请求格式转换为华为mindIE框架格式
    
    输入示例:
        {"input":["Hello World"], "model":"text-embedding-ada-002"}
    输出示例:
        {"inputs": "Hello World"}
    
    Args:
        request_body: 原始请求体（dict）
    
    Returns:
        dict: 转换后的请求体
    """
    try:
        if 'input' in request_body:
            # 处理列表格式输入
            if isinstance(request_body['input'], list) and len(request_body['input']) > 0:
                request_body['inputs'] = request_body['input'][0]
            # 处理字符串直接输入
            elif isinstance(request_body['input'], str):
                request_body['inputs'] = request_body['input']
            del request_body['input']
        
        # 移除模型参数（后端不需要）
        if 'model' in request_body:
            del request_body['model']
            
    except Exception as e:
        print(f"[ERROR] Embedding模型转换失败: {str(e)}")
        raise
    
    return request_body

def map_embedding_response(response, model_name="text-embedding-ada-002"):
    """
    将华为mindIE框架的嵌入响应映射回OpenAI兼容格式
    
    输入示例:
[
    [
        -0.03160575, 0.024146125, -0.028443316
    ],
    [
        -0.022991445, 0.03133917, -0.02478168
    ]
]
    转换后的示例:
        {
        "object": "embedding",
        "embedding": [
       {
            'object': 'embedding', 'index': 0, 
            'embedding': [-0.03160575, 0.024146125, -0.028443316]
        },
        {
             'object': 'embedding', 'index': 1, 
             'embedding': [-0.022991445, 0.03133917, -0.02478168]
         }
        ],
        'model': 'text-embedding-ada-002-v2',
        'usage': {'prompt_tokens': 23, 'total_tokens': 23}
        }
    
    Args:
        response: 原始响应数据（dict）
        model_name: 的模型名称（默认为text-embedding-ada-002）
    
    Returns:
        json: 映射后的openai兼容格式
    """
    try:
        # 处理嵌入数据
        data = []
        for index, embedding in enumerate(response):
           # print(f"[INFO] Embedding数据: {embedding}")
            data.append({
                "object": "embedding",
                "index": index,
                "embedding": embedding
            })
        
        # 返回标准格式
        return {
            "data": data,
            "model": model_name,
            "usage": {
                # 假设每个嵌入对应一个token
                "prompt_tokens": len(response),  
                "total_tokens": len(response)
            }
        }
    except Exception as e:
        print(f"[ERROR] Embedding响应映射失败: {str(e)}")
        raise

def parse_chunked_data(stream):
    """
    解析 Chunked Transfer Encoding 格式的数据。
    
    Args:
        stream: 输入流
    
    Returns:
        str: 解析后的完整数据
    """
    data = b''
    while True:
        # 读取块长度（十六进制）
        line = stream.readline().strip()
        if not line:
            break
        # length = int(line, 16)
        # if length == 0:
        #     # 读取尾部的空行和额外的头部信息（如果存在）
        #     stream.readline()
        #     break
        # # 读取块内容
        # chunk = stream.read(length)
        data += line
        # 读取块之间的空行
        # stream.readline()
    # 返回json
    return json.loads(data)

def stream_response(response, accept_sse=True):
    def generate():
        content = ""
        # 明确指定UTF-8编码处理
        for chunk in response.iter_content(chunk_size=1024, decode_unicode=False):
            try:
               # 强制UTF-8解码并替换错误字符
                chunk_str = chunk.decode('utf-8', errors='replace')
                
                # 完整SSE格式封装
                if accept_sse:
                    for line in chunk_str.split('\n'):
                        if line.strip():
                            #print(f"{line}")  # 调试信息
                            # json解析提取content
                            if line.startswith("data: "):
                                try:
                                    chunk = json.loads(line[6:])  # 去除"data: "前缀并解析
                                    delta_content = chunk["choices"][0]["delta"].get("content", "")
                                    if delta_content:                            
                                        content += delta_content
                                except json.JSONDecodeError:
                                    print(f"[ERROR] JSON解析失败: {line}")
                                    #continue
                            yield f"{line}\n\n"  # 严格遵循SSE格式
                else:
                    yield chunk_str
                    
            except UnicodeDecodeError:
                # 异常编码处理
                yield f"data: {{'error': 'invalid encoding'}}\n\n"
        #print(f"[INFO] 流式数据: {content}")

    # 添加必要响应头
    headers = {
        'Content-Type': 'text/event-stream; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no'  # 禁用Nginx等代理的缓冲
    }
    return app.response_class(generate(), mimetype='text/event-stream', headers=headers)

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():

    return convert_body(request,"/v1/chat/completions")

@app.route('/v1/completions', methods=['POST'])
def completions():

    return convert_body(request,"/v1/completions")

@app.route('/v1/embeddings', methods=['POST'])
def embed():
    return convert_body(request,"/embed")

@app.route('/v1/models', methods=['GET'])
def list_models():
    # 转发模型列表请求
    return convert_body(request,"/v1/models","GET")

@app.route('/anthropic/v1/messages', methods=['POST'])
def anthropic_messages():

    return convert_body(request,"/v1/chat/completions")

def test_body():
    content1 = """
好的，我们已经获取到了一个SM4密钥ID，即1。现在我们可以使用这个密钥ID来加密数据“明文”。\n\n接下来，我们将调用`sm4_encrypt`函数来进行加密操作：\n\n{"name": "sm4_encrypt", "arguments": "{\\"key_id\\": 1, \\"plaintext\\": \\"明文\\"}"}\n
    """
    content2 = """
首先，我们需要确认是否有可用的SM4密钥。如果没有，我们将生成一个新的SM4密钥。请稍等，我将先查询一下现有的SM4密钥。\n\n{\"name\": \"query_keys\", \"arguments\": {\"key_type\": \"SM4\"}}
    """
    tool_call_pattern = re.compile(
        r'\{"name"\s*:\s*"([^"]+)"\s*,\s*"arguments"\s*:\s*(?:"({.*?})"|({.*?}))\s*\}',
        re.DOTALL
    )
    
    for content in [content1, content2]:
        tool_calls = []
        matches = tool_call_pattern.finditer(content)
        for match in matches:
            try:
                # 判断匹配类型
                if match.group(2):  # 字符串包裹的 JSON
                    expr_str = match.group(2)
                    unescaped_str = json.loads(f'"{expr_str}"')
                    arguments = json.loads(unescaped_str)
                else:  # 直接 JSON 对象
                    expr_str = match.group(3)
                    arguments = json.loads(expr_str)
                
                tool_calls.append({
                    "id": f"call_{uuid.uuid4().hex[:12]}",
                    "type": "function",
                    "function": {
                        "name": match.group(1),
                        "arguments": json.dumps(arguments, ensure_ascii=False)
                    }
                })
                
            except json.JSONDecodeError as e:
                print(f"解析失败: {e}")
                continue
        
        print(f"Content: {content.strip()}")
        print(f"Tool Calls: {tool_calls}\n")

def test_embed_response():
    response = """
    [
        [-0.03160575, 0.024146125, -0.028443316],
        [-0.022991445, 0.03133917, -0.02478168]
    ]
    """
    embeddings = map_embedding_response(json.loads(response))
    print(embeddings)

if __name__ == "__main__":
    # 加载模型配置
    #test_embed_response()
    logging.basicConfig(
        filename='error.log',
        level=logging.ERROR,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    model_mapping, mindie_addreess = load_model_config("model_config.ini")
    app.run(host='0.0.0.0', port=5005, debug=True)
