# API转换代理
本项目用于将openai兼容的api转为华为mindIE框架兼容的API。

## 功能概述
### 1.content空
当content为空时，华为mindeIE框架会报错。因此，我们需要在请求华为mindIE框架之前，检查content是否为空，并将其替换为一个默认值（例如：" "）。
对话模型的content的示例结构如下：
```json
{
  "messages": [
    {
      "role": "user",
      "content": ""
    }
  ]
}
```
替换为空格：
```json
{
  "messages": [
    {
      "role": "user",
      "content": " "
    }
  ]
}
```
多模态模型的content的示例结构如下：
```json
{
  "messages": [
    {
      "role": "user",
      "content": {
            "type": "text",
            "text": ""
      }
    }
  ]
}
```
替换为空格：
```json
{
  "messages": [
    {
      "role": "user",
      "content": {
            "type": "text",
            "text": " "
      }
    }
  ]
}
```
### 2.对话模型的api body转换
华为mindIE框架的对话模型只接受context是字符串的的json，如下context是结构体的json需要转换为：
```json
{
  "messages": [
    {
      "role": "user",
      "content": {
            "type": "text",
            "text": "介绍自己"
      }
    }
  ]
}
```
转换为 content type 的json：
```json
{
  "messages": [
    {
      "role": "user",
      "content": "介绍自己"
    }
  ]
}
```

### 3.多模态模型的api body转换
华为mindIE框架的多模特模型只接受context type的json，如下json需要转换为context type的json：
```json
{
  "messages": [
    {
      "role": "user",
      "content": "介绍自己"
    }
  ]
}
```
转换为 content type 的json：
```json
{
  "messages": [
    {
      "role": "user",
      "content": {
            "type": "text",
            "text": "介绍自己"
      }
    }
  ]
}
```
### 4.模型类型识别
可提取请求body中的model字段，用于识别模型类型。
通过配置文件，可以配置不同模型对应的api body转换规则。

### 5.返回结果映射
将华为mindIE框架的返回结果映射回openai兼容的格式。

### 6.工具调用提取
从content中提取工具调用信息，并转换为openai兼容的格式。

### 7. 嵌入模型转换
将嵌入模型转换为openai兼容的格式。
原请求url： /v1/embeddings
原请求body：
```json
{"input":["Hello World"],"model":"text-embedding-ada-002"}
```
转换为后台调用url地址(独立地址,与/v1/chat/completions的后端地址不同)：http://10.0.51.251:18080/embed
转换后的请求body：
```json
{
    "inputs": "Hello World"
}
 ```
 嵌入模型的响应是如下格式:
 ```json
[
    [
        -0.03160575, 0.024146125, -0.028443316
    ],
    [
        -0.022991445, 0.03133917, -0.02478168
    ]
]
 ```
 转换为openai兼容的格式：
 ```json
 {
        "object": "embedding",
        "embedding": [
       {
            'object': 'embedding', 'index': 0, 
            'embedding': [-0.03160575, 0.024146125, -0.028443316]
        },
        {
             'object': 'embedding', 'index': 1, 
             'embedding': [-0.022991445, 0.03133917, -0.02478168]
         }
        ],
        'model': 'text-embedding-ada-002-v2',
        'usage': {'prompt_tokens': 23, 'total_tokens': 23}
        }
 ```