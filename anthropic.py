# anthropic_openai_bridge.py
import os, json, uuid, time
from typing import List, Dict, Any, Async<PERSON><PERSON><PERSON>, Optional, Union
from pydantic import BaseModel, Field
from openai import AsyncOpenAI
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse

class ContentBlock(BaseModel):
    type: str  # text, image, tool_use, tool_result
    text: Optional[str] = None
    source: Optional[Dict] = None  # image
    id: Optional[str] = None       # tool_use
    name: Optional[str] = None     # tool_use
    input: Optional[Dict] = None   # tool_use
    tool_use_id: Optional[str] = None  # tool_result
    content: Optional[str] = None      # tool_result

class Message(BaseModel):
    role: str  # user / assistant
    content: Union[str, List[ContentBlock]]

class ToolFunction(BaseModel):
    name: str
    description: str
    parameters: Dict[str, Any]

class Tool(BaseModel):
    type: str = "function"
    function: ToolFunction

class AnthropicRequest(BaseModel):
    model: str
    messages: List[Message]
    max_tokens: int = 4096
    temperature: float = 1.0
    system: Optional[str] = None
    tools: Optional[List[Tool]] = None
    stream: bool = False
    tool_choice: Any = None
def anthropic_to_openai_messages(req: AnthropicRequest) -> List[Dict[str, Any]]:
    openai_msgs = []
    if req.system:
        openai_msgs.append({"role": "system", "content": req.system})
    for m in req.messages:
        if isinstance(m.content, str):
            openai_msgs.append({"role": m.role, "content": m.content})
        else:
            parts = []
            for block in m.content:
                if block.type == "text":
                    parts.append({"type": "text", "text": block.text})
                elif block.type == "image":
                    parts.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:{block.source['media_type']};base64,{block.source['data']}"
                        }
                    })
                elif block.type == "tool_result":
                    parts.append({
                        "type": "text",
                        "text": block.content
                    })
            openai_msgs.append({"role": m.role, "content": parts})
    return openai_msgs

def anthropic_to_openai_tools(req: AnthropicRequest) -> Optional[List[Dict]]:
    if not req.tools:
        return None
    return [{"type": "function", "function": t.function.dict()} for t in req.tools]

def openai_to_anthropic_response(openai_resp: Dict, model: str) -> Dict[str, Any]:
    msg = openai_resp["choices"][0]["message"]
    content = []
    if msg.get("content"):
        content.append({"type": "text", "text": msg["content"]})
    if msg.get("tool_calls"):
        for tc in msg["tool_calls"]:
            content.append({
                "type": "tool_use",
                "id": tc["id"],
                "name": tc["function"]["name"],
                "input": json.loads(tc["function"]["arguments"])
            })
    return {
        "id": openai_resp["id"],
        "type": "message",
        "role": "assistant",
        "content": content,
        "model": model,
        "stop_reason": openai_resp["choices"][0]["finish_reason"],
        "usage": {
            "input_tokens": openai_resp["usage"]["prompt_tokens"],
            "output_tokens": openai_resp["usage"]["completion_tokens"]
        }
    }
async def openai_to_anthropic_stream(openai_stream, model: str) -> AsyncIterator[str]:
    created = int(time.time())
    id = f"msg_{uuid.uuid4().hex}"
    async for chunk in openai_stream:
        delta = chunk.choices[0].delta
        payload = {
            "type": "content_block_delta",
            "index": 0,
            "delta": {}
        }
        if delta.content:
            payload["delta"]["text"] = delta.content
        if delta.tool_calls:
            # 工具调用增量（只取第一个）
            tc = delta.tool_calls[0]
            if tc.function.name:
                payload["delta"]["type"] = "tool_use"
                payload["delta"]["id"] = tc.id
                payload["delta"]["name"] = tc.function.name
            if tc.function.arguments:
                payload["delta"]["partial_json"] = tc.function.arguments
        yield f"data: {json.dumps(payload)}\n\n"
    yield "data: [DONE]\n\n"

openai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"),
                            base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"))

app = FastAPI()

@app.post("/anthropic/v1/messages")
async def anthropic_endpoint(req: AnthropicRequest):
    openai_msgs = anthropic_to_openai_messages(req)
    tools = anthropic_to_openai_tools(req)
    kwargs = {
        "model": req.model,
        "messages": openai_msgs,
        "max_tokens": req.max_tokens,
        "temperature": req.temperature,
        "stream": req.stream,
    }
    if tools:
        kwargs["tools"] = tools
        kwargs["tool_choice"] = req.tool_choice

    if req.stream:
        openai_stream = await openai_client.chat.completions.create(**kwargs)
        return StreamingResponse(
            openai_to_anthropic_stream(openai_stream, req.model),
            media_type="text/plain"
        )
    else:
        openai_resp = await openai_client.chat.completions.create(**kwargs)
        return openai_to_anthropic_response(openai_resp.model_dump(), req.model)

#pip install openai fastapi uvicorn pydantic
#export OPENAI_API_KEY="sk-xxx"

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5005)